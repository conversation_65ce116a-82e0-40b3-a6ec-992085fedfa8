#!/usr/bin/env python3
"""
Auto Deploy ke Render.com via GitHub
"""
import requests
import json
import base64
import os
import time

def create_github_repo():
    """Create GitHub repository"""
    print("📦 Creating GitHub repository...")
    
    # GitHub API endpoint
    url = "https://api.github.com/user/repos"
    
    # Repository data
    repo_data = {
        "name": "smart-ball-detection-iot",
        "description": "🚀 Smart Ball Detection System - IoT Project by Alvin, Daffa, Abidzar, Ridho",
        "private": False,
        "auto_init": True,
        "gitignore_template": "Python"
    }
    
    print("🔑 Need GitHub Personal Access Token")
    print("📝 Go to: https://github.com/settings/tokens")
    print("📝 Generate new token (classic) with 'repo' scope")
    
    token = input("Enter GitHub token: ").strip()
    
    if not token:
        print("❌ GitHub token required!")
        return None, None
    
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    response = requests.post(url, headers=headers, json=repo_data)
    
    if response.status_code == 201:
        repo_info = response.json()
        print(f"✅ Repository created: {repo_info['html_url']}")
        return repo_info['full_name'], token
    else:
        print(f"❌ Failed to create repository: {response.text}")
        return None, None

def upload_files_to_github(repo_name, token):
    """Upload files to GitHub repository"""
    print("📁 Uploading files to GitHub...")
    
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    files_to_upload = {
        'app_flask.py': 'Main Flask application',
        'requirements.txt': 'Python dependencies',
        'wsgi.py': 'WSGI configuration',
        'Procfile': 'Process configuration',
        'render.yaml': 'Render configuration',
        'README.md': 'Project documentation',
        'templates/index.html': 'HTML template'
    }
    
    for file_path, description in files_to_upload.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Encode content to base64
                content_b64 = base64.b64encode(content.encode()).decode()
                
                # GitHub API data
                data = {
                    "message": f"Add {description}",
                    "content": content_b64
                }
                
                # Upload file
                url = f"https://api.github.com/repos/{repo_name}/contents/{file_path}"
                response = requests.put(url, headers=headers, json=data)
                
                if response.status_code == 201:
                    print(f"✅ Uploaded: {file_path}")
                else:
                    print(f"❌ Failed to upload {file_path}: {response.text}")
                    
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                print(f"❌ Error uploading {file_path}: {e}")
        else:
            print(f"⚠️ File not found: {file_path}")

def deploy_to_render(repo_name):
    """Deploy to Render.com"""
    print("🚀 Deploying to Render...")
    
    render_url = f"https://dashboard.render.com/select-repo?type=web"
    
    print(f"🌐 Manual step required:")
    print(f"1. Go to: {render_url}")
    print(f"2. Connect GitHub account")
    print(f"3. Select repository: {repo_name}")
    print(f"4. Configure:")
    print(f"   - Name: smart-ball-detection-iot")
    print(f"   - Language: Python 3")
    print(f"   - Build Command: pip install -r requirements.txt")
    print(f"   - Start Command: gunicorn app_flask:app")
    print(f"   - Plan: Free")
    print(f"5. Click 'Create Web Service'")
    
    return True

def main():
    print("🎯 Smart Ball Detection System - Auto Deploy to Render")
    print("Made by: Alvin, Daffa, Abidzar, Ridho")
    print("=" * 60)
    
    # Create GitHub repo
    repo_name, token = create_github_repo()
    
    if repo_name and token:
        # Upload files
        upload_files_to_github(repo_name, token)
        
        # Deploy to Render
        deploy_to_render(repo_name)
        
        print("\n🎉 Setup complete!")
        print(f"📦 GitHub Repo: https://github.com/{repo_name}")
        print(f"🚀 Deploy URL: https://dashboard.render.com/")
        print(f"🌐 Final URL will be: https://smart-ball-detection-iot.onrender.com")
        
    else:
        print("\n❌ Failed to create GitHub repository")
        print("\n🔄 Alternative: Manual upload")
        print("1. Create GitHub repo manually")
        print("2. Upload smart-ball-detection-deploy.zip")
        print("3. Deploy to Render.com")

if __name__ == "__main__":
    main()
