#!/usr/bin/env python3
"""
Smart Ball Detection System - IoT Project
Made by: <PERSON>, <PERSON><PERSON>, Abid<PERSON>, Ridho
Super Simple Gradio 3.x Version (100% Working!)
"""

import gradio as gr
import requests
from PIL import Image, ImageDraw
import numpy as np
import base64
from io import BytesIO

# Roboflow API configuration - FIXED VERSION LIKE FLASK APP
API_KEY = "Am7lrBZW1eYzrwt0rWvc"
MODEL_ID = "ball-t8zxj/15"

def correct_ball_type(pred, image_size):
    """Correct ball type based on size and context - SAME AS FLASK"""
    width, height = pred['width'], pred['height']
    img_w, img_h = image_size

    # Calculate relative size
    relative_size = (width * height) / (img_w * img_h)

    # Size-based correction logic
    if relative_size < 0.05:  # Very small = likely golf ball
        if pred['class'].lower() in ['basketball', 'football', 'soccer']:
            pred['class'] = 'golf'
            pred['confidence'] = min(pred['confidence'], 0.75)  # Lower confidence for correction
    elif relative_size > 0.3:  # Very large = likely basketball/football
        if pred['class'].lower() == 'golf':
            pred['class'] = 'basketball'
            pred['confidence'] = min(pred['confidence'], 0.75)

    return pred

def try_multiple_models(img_base64):
    """Try multiple models for better accuracy - SAME AS FLASK"""
    models_to_try = [
        "golf-ball-detection-hii2e/1",  # Golf specific
        "anna-gaming/golfball/1",       # Alternative golf
        "ball-t8zxj/15",               # Original general
    ]

    for model_id in models_to_try:
        try:
            url = f"https://serverless.roboflow.com/{model_id}"
            payload = {
                "image": img_base64,
                "api_key": API_KEY
            }
            headers = {"Content-Type": "application/json"}

            response = requests.post(url, json=payload, headers=headers, timeout=15)

            if response.status_code == 200:
                result = response.json()
                predictions = result.get('predictions', [])
                if predictions:  # If we get results, use this model
                    return predictions, model_id
        except:
            continue

    return [], None

def detect_balls_simple(image):
    """Detect balls using multiple models with post-processing - FIXED VERSION"""
    if image is None:
        return None, "❌ Please upload an image first!"

    try:
        # Convert to PIL if needed
        if isinstance(image, np.ndarray):
            image = Image.fromarray(image)

        print(f"📏 Image size: {image.size}")

        # Convert to base64 for API
        buffer = BytesIO()
        image.save(buffer, format='JPEG', quality=95)
        img_base64 = base64.b64encode(buffer.getvalue()).decode()

        # Try multiple models - SAME AS FLASK
        predictions, used_model = try_multiple_models(img_base64)

        if predictions:
            # Apply post-processing corrections - SAME AS FLASK
            corrected_predictions = []
            for pred in predictions:
                corrected_pred = correct_ball_type(pred, image.size)
                corrected_predictions.append(corrected_pred)

            # Draw boxes
            result_image = draw_boxes(image, corrected_predictions)

            # Create result text with model info
            result_text = f"🎉 Success! Found {len(corrected_predictions)} ball(s)!\n"
            result_text += f"🤖 Model Used: {used_model}\n\n"

            for i, pred in enumerate(corrected_predictions):
                ball_emoji = "⚽" if "golf" in pred['class'].lower() else "🏀"
                result_text += f"{ball_emoji} Ball {i+1}: {pred['class'].title()}\n"
                result_text += f"   • Confidence: {pred['confidence']:.1%}\n"
                result_text += f"   • Position: x={pred['x']:.0f}, y={pred['y']:.0f}\n"
                result_text += f"   • Size: {pred['width']:.0f} × {pred['height']:.0f}\n"

                # Show if corrected
                if pred['confidence'] <= 0.75:
                    result_text += f"   ⚠️ Detection corrected based on size analysis\n"
                result_text += "\n"

            return result_image, result_text
        else:
            return image, "⚠️ No balls detected\n\nTry uploading a clearer image with sports balls."

    except Exception as e:
        print(f"❌ Error: {e}")
        return image, f"❌ Error: {str(e)}"

def draw_boxes(image, predictions):
    """Draw simple bounding boxes"""
    try:
        result_image = image.copy()
        draw = ImageDraw.Draw(result_image)
        
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, pred in enumerate(predictions):
            x, y = pred['x'], pred['y']
            w, h = pred['width'], pred['height']
            
            x1, y1 = x - w/2, y - h/2
            x2, y2 = x + w/2, y + h/2
            
            color = colors[i % len(colors)]
            
            # Draw thick box
            for t in range(3):
                draw.rectangle([x1-t, y1-t, x2+t, y2+t], outline=color, width=1)
            
            # Label
            label = f"{pred['class'].title()} {pred['confidence']:.1%}"
            draw.rectangle([x1, y1-25, x1+len(label)*8, y1], fill=color)
            draw.text((x1+5, y1-20), label, fill='white')
        
        return result_image
    except:
        return image

# Create interface using Gradio 3.x Interface (not Blocks)
title = "⚽🏀🏐 Smart Ball Detection System"

description = """
🤖 **AI-Powered Computer Vision for Sports Ball Recognition**

🎓 **IoT Project by: Alvin, Daffa, Abidzar, Ridho**

Upload an image containing sports balls and get instant AI detection results with bounding boxes!

**Supported balls:** Football ⚽ • Basketball 🏀 • Tennis 🎾 • Volleyball 🏐 • Golf 🏌️ • Baseball ⚾
"""

article = """
---
### 🚀 Features
- **🎯 Multi-Ball Detection**: Detects various types of sports balls
- **🤖 AI-Powered**: Uses Roboflow Computer Vision for high accuracy
- **📱 Easy to Use**: Simply upload an image and get instant results
- **🌐 Mobile Friendly**: Works on all devices

### 🎓 Project Info
**Team:** Alvin, Daffa, Abidzar, Ridho  
**AI Engine:** Roboflow Computer Vision  
**Platform:** Hugging Face Spaces  

✨ Built with ❤️ using modern web technologies • 🎯 Accurate ball detection • 📱 Mobile responsive
"""

# Create simple interface
demo = gr.Interface(
    fn=detect_balls_simple,
    inputs=gr.Image(type="pil", label="📁 Upload Image with Sports Balls"),
    outputs=[
        gr.Image(type="pil", label="🖼️ Result with Bounding Boxes"),
        gr.Textbox(label="🎯 Detection Results", lines=10)
    ],
    title=title,
    description=description,
    article=article,
    theme="default",
    allow_flagging="never"
)

if __name__ == "__main__":
    demo.launch(share=True)
