const express = require('express');
const axios = require('axios');
const multer = require('multer');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// Multer config for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = 'uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({ 
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// Roboflow API configuration
const API_KEY = "Am7lrBZW1eYzrwt0rWvc";
const MODEL_ID = "ball-t8zxj/15";

// Ball detection function
async function detectBalls(imageBase64) {
    try {
        console.log('🚀 Sending to Roboflow API...');
        
        const response = await axios({
            method: "POST",
            url: `https://serverless.roboflow.com/${MODEL_ID}`,
            params: {
                api_key: API_KEY
            },
            data: imageBase64,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            },
            timeout: 30000
        });

        console.log(`🎯 Found ${response.data.predictions?.length || 0} balls!`);
        return response.data;
        
    } catch (error) {
        console.error('❌ API Error:', error.message);
        throw error;
    }
}

// Convert file to base64
function fileToBase64(filePath) {
    return fs.readFileSync(filePath, { encoding: "base64" });
}

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Upload and detect endpoint
app.post('/detect', upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No image uploaded' });
        }

        console.log(`📁 Processing: ${req.file.filename}`);
        
        // Convert to base64
        const imageBase64 = fileToBase64(req.file.path);
        
        // Detect balls
        const result = await detectBalls(imageBase64);
        
        // Clean up uploaded file
        fs.unlinkSync(req.file.path);
        
        res.json({
            success: true,
            predictions: result.predictions || [],
            image: result.image || {},
            message: `Found ${result.predictions?.length || 0} ball(s)!`
        });
        
    } catch (error) {
        console.error('❌ Detection error:', error.message);
        res.status(500).json({ 
            error: 'Detection failed', 
            message: error.message 
        });
    }
});

// Detect from URL endpoint
app.post('/detect-url', async (req, res) => {
    try {
        const { imageUrl } = req.body;
        
        if (!imageUrl) {
            return res.status(400).json({ error: 'No image URL provided' });
        }

        console.log(`🌐 Processing URL: ${imageUrl}`);
        
        const response = await axios({
            method: "POST",
            url: `https://serverless.roboflow.com/${MODEL_ID}`,
            params: {
                api_key: API_KEY,
                image: imageUrl
            }
        });

        res.json({
            success: true,
            predictions: response.data.predictions || [],
            image: response.data.image || {},
            message: `Found ${response.data.predictions?.length || 0} ball(s)!`
        });
        
    } catch (error) {
        console.error('❌ URL Detection error:', error.message);
        res.status(500).json({ 
            error: 'Detection failed', 
            message: error.message 
        });
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'Smart Ball Detection API is running!',
        model: MODEL_ID,
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 Smart Ball Detection Server Started!');
    console.log(`🌐 Server running on port: ${PORT}`);
    console.log(`🔍 Model ID: ${MODEL_ID}`);
    console.log(`🎓 IoT Project by: Alvin, Daffa, Abidzar, Ridho`);
    console.log(`📱 Access: http://localhost:${PORT}`);
});
