#!/usr/bin/env python3
"""
Streamlit version of Smart Ball Detection System
For easy deployment to Streamlit Cloud
"""
import streamlit as st
import requests
import base64
from PIL import Image
import io
import os
from inference_sdk import InferenceHTTPClient

# Page config
st.set_page_config(
    page_title="Smart Ball Detection System",
    page_icon="⚽",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS - Super Cantik!
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    .main {
        font-family: 'Inter', sans-serif;
    }

    .main-header {
        text-align: center;
        padding: 3rem 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        margin-bottom: 3rem;
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .main-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.1;
    }

    .main-header h1 {
        font-size: 3.5rem !important;
        font-weight: 800 !important;
        margin-bottom: 1rem !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
        z-index: 1;
    }

    .main-header p {
        font-size: 1.3rem !important;
        font-weight: 400 !important;
        opacity: 0.95;
        position: relative;
        z-index: 1;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
        margin: 1rem 0;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .upload-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 2rem;
        border-radius: 20px;
        border: 2px dashed #667eea;
        text-align: center;
        margin: 2rem 0;
    }

    .result-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
        margin: 1rem 0;
    }

    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        margin: 0.5rem;
    }

    .footer {
        text-align: center;
        padding: 3rem 2rem;
        margin-top: 4rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 20px;
        border-top: 3px solid #667eea;
    }

    .stButton > button {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .sample-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 0.8rem 1.5rem;
        border-radius: 10px;
        border: none;
        font-weight: 500;
        margin: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .sample-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
    }

    .prediction-badge {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
        margin: 0.25rem;
    }

    .confidence-bar {
        background: linear-gradient(90deg, #10b981 0%, #059669 100%);
        height: 8px;
        border-radius: 4px;
        margin: 0.5rem 0;
    }

    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .main-header h1 {
            font-size: 2.5rem !important;
        }
        .main-header p {
            font-size: 1.1rem !important;
        }
        .feature-card {
            padding: 1.5rem;
        }
    }
</style>
""", unsafe_allow_html=True)

# Initialize Inference Client
@st.cache_resource
def init_client():
    try:
        client = InferenceHTTPClient(
            api_url="https://serverless.roboflow.com",
            api_key="Am7lrBZW1eYzrwt0rWvc"
        )
        return client
    except Exception as e:
        st.error(f"Failed to initialize AI model: {e}")
        return None

def detect_balls(image, client):
    """Detect balls in image"""
    if client is None:
        return {"error": "AI model not available"}
    
    try:
        # Convert PIL image to bytes
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        
        # Save temporarily
        temp_path = "temp_image.jpg"
        with open(temp_path, "wb") as f:
            f.write(img_buffer.getvalue())
        
        # Predict
        result = client.infer(temp_path, model_id="ball-t8zxj/15")
        
        # Clean up
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        return result
        
    except Exception as e:
        return {"error": str(e)}

def main():
    # Header - Super Cantik!
    st.markdown("""
    <div class="main-header animate-fade-in">
        <h1>⚽🏀🏐 Smart Ball Detection System</h1>
        <p>🤖 AI-Powered Computer Vision for Sports Ball Recognition</p>
        <p>🎓 <strong>IoT Project by: Alvin, Daffa, Abidzar, Ridho</strong></p>
        <p style="margin-top: 1.5rem; font-size: 1rem; opacity: 0.8;">
            ✨ Powered by Roboflow AI • 🚀 Built with Streamlit • 📱 Mobile Friendly
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize client
    client = init_client()
    
    if client is None:
        st.error("❌ AI model initialization failed. Please refresh the page.")
        return
    
    # Main content
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("""
        <div class="feature-card animate-fade-in">
            <h3 style="color: #667eea; margin-bottom: 1rem;">📤 Upload Your Image</h3>
            <p style="color: #64748b; margin-bottom: 1.5rem;">
                Choose an image containing sports balls for AI detection
            </p>
        </div>
        """, unsafe_allow_html=True)

        uploaded_file = st.file_uploader(
            "📁 Select Image File",
            type=['jpg', 'jpeg', 'png'],
            help="🎯 Supported: Football, Basketball, Tennis, Volleyball, Golf, Baseball"
        )
        
        if uploaded_file is not None:
            # Display uploaded image
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Image", use_column_width=True)
            
            # Detect button
            if st.button("🎯 Detect Balls", key="detect_btn"):
                with st.spinner("🤖 AI is analyzing your image..."):
                    result = detect_balls(image, client)
                
                # Show results in second column
                with col2:
                    st.markdown("""
                    <div class="result-card animate-fade-in">
                        <h3 style="color: #667eea; margin-bottom: 1rem;">🎯 AI Detection Results</h3>
                    </div>
                    """, unsafe_allow_html=True)

                    if "error" in result:
                        st.markdown("""
                        <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                                    color: white; padding: 1rem; border-radius: 12px; margin: 1rem 0;">
                            <h4>❌ Detection Error</h4>
                            <p>{}</p>
                        </div>
                        """.format(result['error']), unsafe_allow_html=True)
                    else:
                        predictions = result.get('predictions', [])

                        if predictions:
                            st.markdown(f"""
                            <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                                        color: white; padding: 1.5rem; border-radius: 12px; margin: 1rem 0; text-align: center;">
                                <h3>🎉 Success!</h3>
                                <p style="font-size: 1.2rem; margin: 0;">Found <strong>{len(predictions)}</strong> ball(s) in your image!</p>
                            </div>
                            """, unsafe_allow_html=True)

                            # Show predictions with beautiful cards
                            for i, pred in enumerate(predictions):
                                confidence_percent = pred['confidence'] * 100

                                st.markdown(f"""
                                <div class="feature-card" style="margin: 1rem 0;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <h4 style="color: #667eea; margin: 0;">🎾 Ball {i+1}: {pred['class'].upper()}</h4>
                                        <span class="prediction-badge">{confidence_percent:.1f}%</span>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                        <div class="metric-card">
                                            <h5 style="margin: 0; opacity: 0.9;">Confidence</h5>
                                            <h3 style="margin: 0.5rem 0 0 0;">{confidence_percent:.1f}%</h3>
                                        </div>
                                        <div class="metric-card">
                                            <h5 style="margin: 0; opacity: 0.9;">Type</h5>
                                            <h3 style="margin: 0.5rem 0 0 0;">{pred['class'].title()}</h3>
                                        </div>
                                    </div>

                                    <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                                        <p style="margin: 0; color: #64748b;">
                                            <strong>📍 Position:</strong> x={pred['x']:.0f}, y={pred['y']:.0f}<br>
                                            <strong>📏 Size:</strong> {pred['width']:.0f} × {pred['height']:.0f} pixels
                                        </p>
                                    </div>

                                    <div class="confidence-bar" style="width: {confidence_percent}%;"></div>
                                </div>
                                """, unsafe_allow_html=True)
                        else:
                            st.markdown("""
                            <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                                        color: white; padding: 1.5rem; border-radius: 12px; margin: 1rem 0; text-align: center;">
                                <h4>⚠️ No Balls Detected</h4>
                                <p>Try uploading a clearer image with sports balls visible</p>
                                <p style="font-size: 0.9rem; opacity: 0.9;">
                                    💡 Tip: Make sure the balls are clearly visible and well-lit
                                </p>
                            </div>
                            """, unsafe_allow_html=True)
    
    with col2:
        if uploaded_file is None:
            st.subheader("🎯 Detection Results")
            st.info("👆 Upload an image to see AI detection results here!")
            
            # Sample images
            st.subheader("📸 Try Sample Images")
            sample_images = {
                "🏀 Basketball": "samples/basketball.jpg",
                "⚽ Football": "samples/football.jpg", 
                "🎾 Tennis": "samples/tennis.jpg",
                "🏐 Volleyball": "samples/volleyball.jpg"
            }
            
            for name, path in sample_images.items():
                if os.path.exists(path):
                    if st.button(f"Try {name}", key=f"sample_{name}"):
                        sample_image = Image.open(path)
                        st.image(sample_image, caption=f"Sample: {name}", use_column_width=True)
                        
                        with st.spinner("🤖 Analyzing sample..."):
                            result = detect_balls(sample_image, client)
                        
                        if "error" not in result:
                            predictions = result.get('predictions', [])
                            if predictions:
                                st.success(f"✅ Detected {len(predictions)} ball(s) in sample!")
                                for pred in predictions:
                                    st.write(f"• {pred['class'].title()}: {pred['confidence']:.1%}")
    
    # Features section
    st.markdown("---")
    st.subheader("🚀 Features")
    
    feat_col1, feat_col2, feat_col3 = st.columns(3)
    
    with feat_col1:
        st.markdown("""
        **🎯 Multi-Ball Detection**
        - Football/Soccer ⚽
        - Basketball 🏀
        - Volleyball 🏐
        """)
    
    with feat_col2:
        st.markdown("""
        **🤖 AI-Powered**
        - Roboflow Computer Vision
        - High Accuracy Detection
        - Real-time Processing
        """)
    
    with feat_col3:
        st.markdown("""
        **📱 Easy to Use**
        - Upload any image
        - Instant results
        - Mobile friendly
        """)
    
    # Footer - Super Cantik!
    st.markdown("""
    <div class="footer animate-fade-in">
        <h3 style="color: #667eea; margin-bottom: 1.5rem;">🎓 Smart Ball Detection System</h3>
        <p style="font-size: 1.1rem; color: #64748b; margin-bottom: 1rem;">
            <strong>IoT Project:</strong> Advanced Computer Vision for Sports Ball Recognition
        </p>
        <div style="display: flex; justify-content: center; gap: 2rem; margin: 2rem 0; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #667eea; margin-bottom: 0.5rem;">👥 Team</h4>
                <p style="margin: 0; color: #64748b;"><strong>Alvin, Daffa, Abidzar, Ridho</strong></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #667eea; margin-bottom: 0.5rem;">🤖 AI Engine</h4>
                <p style="margin: 0; color: #64748b;">Roboflow Computer Vision</p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #667eea; margin-bottom: 0.5rem;">🚀 Platform</h4>
                <p style="margin: 0; color: #64748b;">Streamlit Cloud</p>
            </div>
        </div>
        <div style="border-top: 2px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
            <p style="color: #94a3b8; font-size: 0.9rem; margin: 0;">
                ✨ Built with ❤️ using modern web technologies • 🎯 Accurate ball detection • 📱 Mobile responsive
            </p>
        </div>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
