#!/usr/bin/env python3
"""
Deploy script untuk Smart Ball Detection System
"""
import os
import subprocess
import sys

def run_command(cmd, cwd=None):
    """Run shell command"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Error: {result.stderr}")
            return False
        print(f"✅ {result.stdout}")
        return True
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🚀 Deploying Smart Ball Detection System...")
    
    # Check if git is initialized
    if not os.path.exists('.git'):
        print("📦 Initializing git...")
        run_command("git init")
        run_command("git branch -M main")
    
    # Add all files
    print("📁 Adding files...")
    run_command("git add .")
    
    # Commit
    print("💾 Committing...")
    run_command('git commit -m "🚀 Smart Ball Detection System - IoT Project by <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"')
    
    print("\n🎯 Next steps:")
    print("1. Create GitHub repo: https://github.com/new")
    print("2. Name: smart-ball-detection-iot")
    print("3. Run: git remote add origin https://github.com/USERNAME/smart-ball-detection-iot.git")
    print("4. Run: git push -u origin main")
    print("5. Deploy to Render: https://dashboard.render.com")
    
    print("\n📋 Render Settings:")
    print("- Name: smart-ball-detection-iot")
    print("- Language: Python 3")
    print("- Build Command: pip install -r requirements.txt")
    print("- Start Command: gunicorn app_flask:app")
    print("- Plan: Free")

if __name__ == "__main__":
    main()
