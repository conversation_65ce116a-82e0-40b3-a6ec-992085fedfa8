#!/usr/bin/env python3
"""
Smart Ball Detection System - IoT Project
Made by: <PERSON>, <PERSON><PERSON><PERSON>, Abid<PERSON>, Ridho
Beautiful Gradio Interface - 100% Mirip Flask Version
"""

import gradio as gr
import requests
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os
import base64
from io import BytesIO
from inference_sdk import InferenceHTTPClient

# Roboflow Inference SDK configuration - Same as Flask
API_KEY = "Am7lrBZW1eYzrwt0rWvc"
MODEL_ID = "ball-t8zxj/15"

# Initialize Inference Client
try:
    CLIENT = InferenceHTTPClient(
        api_url="https://serverless.roboflow.com",
        api_key=API_KEY
    )
    print("✅ Inference client initialized successfully!")
    print(f"🔍 Model ID: {MODEL_ID}")
    print(f"🔍 API URL: https://serverless.roboflow.com")
except Exception as e:
    print(f"❌ Error initializing client: {e}")
    CLIENT = None

def detect_balls_api(image):
    """Detect balls using InferenceHTTPClient - Same as <PERSON><PERSON><PERSON>"""
    if image is None:
        return None, "❌ Please upload an image first!"

    if CLIENT is None:
        return None, "❌ Inference client not available!"

    try:
        # Convert to PIL if needed
        if isinstance(image, np.ndarray):
            image = Image.fromarray(image)

        # Get image info
        print(f"📏 Original image size: {image.size} (width x height)")
        print(f"📁 Image format: {image.format}")

        # Save temporarily for InferenceHTTPClient
        temp_path = "temp_gradio_image.jpg"
        image.save(temp_path, format='JPEG', quality=95)

        # Use InferenceHTTPClient - Same as Flask
        print(f"🚀 Sending to Roboflow Inference API...")
        result = CLIENT.infer(temp_path, model_id=MODEL_ID)

        # Clean up temp file
        if os.path.exists(temp_path):
            os.remove(temp_path)

        # Process results
        predictions = result.get('predictions', [])
        print(f"🔍 API result: {len(predictions)} predictions")

        for i, pred in enumerate(predictions):
            print(f"  🎯 {i+1}: {pred['class']} - {pred['confidence']:.1%}")

        # Draw bounding boxes
        result_image = draw_bounding_boxes_beautiful(image, predictions)

        # Create result text
        if predictions:
            result_text = f"🎉 **Success!** Found **{len(predictions)}** ball(s) in your image!\n\n"
            for i, pred in enumerate(predictions):
                confidence = pred['confidence'] * 100
                result_text += f"🎾 **Ball {i+1}:** {pred['class'].title()}\n"
                result_text += f"   • **Confidence:** {confidence:.1f}%\n"
                result_text += f"   • **Position:** x={pred['x']:.0f}, y={pred['y']:.0f}\n"
                result_text += f"   • **Size:** {pred['width']:.0f} × {pred['height']:.0f} pixels\n\n"
        else:
            result_text = "⚠️ **No balls detected** in this image.\n\n💡 **Tip:** Make sure the image contains sports balls that are clearly visible and well-lit."

        return result_image, result_text

    except Exception as e:
        print(f"❌ Error: {e}")
        # Clean up temp file if exists
        if os.path.exists("temp_gradio_image.jpg"):
            os.remove("temp_gradio_image.jpg")
        return image, f"❌ **Error:** {str(e)}\n\nPlease try uploading a different image."

def draw_bounding_boxes_beautiful(image, predictions):
    """Draw beautiful bounding boxes on image"""
    try:
        # Create a copy
        result_image = image.copy()
        draw = ImageDraw.Draw(result_image)
        
        # Beautiful colors for different classes
        colors = [
            '#667eea',  # Purple-blue
            '#f093fb',  # Pink
            '#4facfe',  # Light blue  
            '#43e97b',  # Green
            '#fa709a',  # Pink-red
            '#ffecd2'   # Yellow
        ]
        
        # Try to load a nice font
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        for i, pred in enumerate(predictions):
            # Get coordinates
            x = pred['x']
            y = pred['y'] 
            width = pred['width']
            height = pred['height']
            
            # Calculate box coordinates
            x1 = x - width/2
            y1 = y - height/2
            x2 = x + width/2
            y2 = y + height/2
            
            # Get color
            color = colors[i % len(colors)]
            
            # Draw thick rectangle with rounded corners effect
            for thickness in range(4):
                draw.rectangle([x1-thickness, y1-thickness, x2+thickness, y2+thickness], 
                             outline=color, width=1)
            
            # Draw label background
            label = f"{pred['class'].title()} {pred['confidence']:.1%}"
            bbox = draw.textbbox((0, 0), label, font=font)
            label_width = bbox[2] - bbox[0]
            label_height = bbox[3] - bbox[1]
            
            # Background rectangle for label
            draw.rectangle([x1, y1-label_height-8, x1+label_width+16, y1], 
                         fill=color, outline=color)
            
            # Draw label text
            draw.text((x1+8, y1-label_height-4), label, fill='white', font=font)
            
            print(f"  📦 {i+1}: {pred['class']} - Box: ({x1:.0f}, {y1:.0f}) to ({x2:.0f}, {y2:.0f})")
        
        print(f"✅ Drew {len(predictions)} beautiful bounding boxes")
        return result_image
        
    except Exception as e:
        print(f"❌ Error drawing boxes: {e}")
        return image

# Custom CSS - 100% Mirip Flask Version
custom_css = """
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Global Styles */
* {
    font-family: 'Inter', sans-serif !important;
}

/* Main Container */
.gradio-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    min-height: 100vh !important;
}

/* Header Styles - Exact Copy */
.header-container {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    margin: 2rem 0;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.header-container h1 {
    font-size: 3.5rem !important;
    font-weight: 800 !important;
    margin-bottom: 1rem !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-container p {
    font-size: 1.3rem !important;
    font-weight: 400 !important;
    opacity: 0.95;
    margin: 0.5rem 0;
}

/* Upload Area */
.upload-area {
    background: white !important;
    border: 3px dashed #667eea !important;
    border-radius: 20px !important;
    padding: 3rem !important;
    text-align: center !important;
    margin: 2rem 0 !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
}

.upload-area:hover {
    border-color: #764ba2 !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

/* Result Section */
.result-section {
    background: white !important;
    border-radius: 20px !important;
    padding: 2rem !important;
    margin: 2rem 0 !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
    border-left: 4px solid #667eea !important;
}

/* Footer - Exact Copy */
.footer-container {
    text-align: center;
    padding: 3rem 2rem;
    margin-top: 4rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    border-top: 3px solid #667eea;
}

.footer-container h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.footer-grid {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.footer-item {
    text-align: center;
}

.footer-item h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.footer-item p {
    margin: 0;
    color: #64748b;
    font-weight: 500;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    padding: 1rem 2rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

/* Image Display */
.image-container {
    border-radius: 16px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

/* Text Output */
.output-text {
    background: #f8fafc !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    border-left: 4px solid #667eea !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header-container h1 {
        font-size: 2.5rem !important;
    }
    .header-container p {
        font-size: 1.1rem !important;
    }
    .footer-grid {
        flex-direction: column;
        gap: 1rem;
    }
}
"""

# Create Gradio Interface
def create_interface():
    with gr.Blocks(css=custom_css, title="⚽ Smart Ball Detection System") as demo:
        
        # Header - Exact Copy dari Flask
        gr.HTML("""
        <div class="header-container">
            <h1>⚽🏀🏐 Smart Ball Detection System</h1>
            <p>🤖 AI-Powered Computer Vision for Sports Ball Recognition</p>
            <p>🎓 <strong>IoT Project by: Alvin, Daffa, Abidzar, Ridho</strong></p>
            <p style="margin-top: 1.5rem; font-size: 1rem; opacity: 0.8;">
                ✨ Powered by Roboflow AI • 🚀 Built with Gradio • 📱 Mobile Friendly
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("""
                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: 0 10px 25px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                    <h3 style="color: #667eea; margin-bottom: 1rem;">📤 Upload Your Image</h3>
                    <p style="color: #64748b; margin-bottom: 1.5rem;">
                        Choose an image containing sports balls for AI detection
                    </p>
                </div>
                """)
                
                input_image = gr.Image(
                    label="📁 Select Image File",
                    type="pil",
                    elem_classes=["upload-area"]
                )
                
                detect_btn = gr.Button(
                    "🎯 Detect Balls", 
                    variant="primary",
                    elem_classes=["btn-primary"]
                )
            
            with gr.Column(scale=1):
                gr.HTML("""
                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: 0 10px 25px rgba(0,0,0,0.1); margin-bottom: 2rem; border-left: 4px solid #667eea;">
                    <h3 style="color: #667eea; margin-bottom: 1rem;">🎯 AI Detection Results</h3>
                    <p style="color: #64748b;">Upload an image to see AI detection results here!</p>
                </div>
                """)
                
                output_image = gr.Image(
                    label="🖼️ Result with Bounding Boxes",
                    elem_classes=["image-container"]
                )
                
                output_text = gr.Markdown(
                    "👆 Upload an image and click **Detect Balls** to see results!",
                    elem_classes=["output-text"]
                )
        
        # Features Section - Mirip Flask
        gr.HTML("""
        <div style="margin: 3rem 0; padding: 2rem; background: white; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
            <h3 style="text-align: center; color: #667eea; margin-bottom: 2rem;">🚀 Features</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div style="text-align: center; padding: 1.5rem;">
                    <h4 style="color: #667eea; margin-bottom: 1rem;">🎯 Multi-Ball Detection</h4>
                    <p style="color: #64748b;">Football/Soccer ⚽<br>Basketball 🏀<br>Volleyball 🏐<br>Tennis 🎾<br>Golf 🏌️<br>Baseball ⚾</p>
                </div>
                <div style="text-align: center; padding: 1.5rem;">
                    <h4 style="color: #667eea; margin-bottom: 1rem;">🤖 AI-Powered</h4>
                    <p style="color: #64748b;">Roboflow Computer Vision<br>High Accuracy Detection<br>Real-time Processing</p>
                </div>
                <div style="text-align: center; padding: 1.5rem;">
                    <h4 style="color: #667eea; margin-bottom: 1rem;">📱 Easy to Use</h4>
                    <p style="color: #64748b;">Upload any image<br>Instant results<br>Mobile friendly</p>
                </div>
            </div>
        </div>
        """)
        
        # Footer - Exact Copy dari Flask
        gr.HTML("""
        <div class="footer-container">
            <h3>🎓 Smart Ball Detection System</h3>
            <p style="font-size: 1.1rem; color: #64748b; margin-bottom: 1rem;">
                <strong>IoT Project:</strong> Advanced Computer Vision for Sports Ball Recognition
            </p>
            <div class="footer-grid">
                <div class="footer-item">
                    <h4>👥 Team</h4>
                    <p><strong>Alvin, Daffa, Abidzar, Ridho</strong></p>
                </div>
                <div class="footer-item">
                    <h4>🤖 AI Engine</h4>
                    <p>Roboflow Computer Vision</p>
                </div>
                <div class="footer-item">
                    <h4>🚀 Platform</h4>
                    <p>Gradio & Hugging Face</p>
                </div>
            </div>
            <div style="border-top: 2px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
                <p style="color: #94a3b8; font-size: 0.9rem; margin: 0;">
                    ✨ Built with ❤️ using modern web technologies • 🎯 Accurate ball detection • 📱 Mobile responsive
                </p>
            </div>
        </div>
        """)
        
        # Event Handler
        detect_btn.click(
            fn=detect_balls_api,
            inputs=[input_image],
            outputs=[output_image, output_text]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_interface()
    demo.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=7860,
        show_error=True
    )
