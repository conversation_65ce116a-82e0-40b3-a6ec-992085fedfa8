#!/usr/bin/env python3
"""
Deploy ke Vercel - Serverless Flask
"""
import os
import json
import zipfile
import requests
import base64

def create_vercel_config():
    """Create vercel.json for Flask deployment"""
    print("Creating Vercel configuration...")
    
    vercel_config = {
        "version": 2,
        "builds": [
            {
                "src": "app_flask.py",
                "use": "@vercel/python"
            }
        ],
        "routes": [
            {
                "src": "/(.*)",
                "dest": "app_flask.py"
            }
        ],
        "env": {
            "FLASK_ENV": "production"
        }
    }
    
    with open('vercel.json', 'w') as f:
        json.dump(vercel_config, f, indent=2)
    
    print("SUCCESS: vercel.json created")

def create_api_folder():
    """Create API folder structure for Vercel"""
    print("Creating API folder structure...")
    
    os.makedirs('api', exist_ok=True)
    
    # Copy main app to api folder
    if os.path.exists('app_flask.py'):
        with open('app_flask.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Modify for Vercel serverless
        content = content.replace(
            "if __name__ == '__main__':",
            "# Vercel serverless handler\napp = app\n\nif __name__ == '__main__':"
        )
        
        with open('api/index.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("SUCCESS: API handler created")

def create_deployment_package():
    """Create deployment package"""
    print("Creating deployment package...")
    
    # Create vercel config
    create_vercel_config()
    
    # Create API structure
    create_api_folder()
    
    # Create ZIP file
    with zipfile.ZipFile('vercel-deploy.zip', 'w') as zipf:
        files_to_include = [
            'api/index.py',
            'vercel.json',
            'requirements.txt',
            'templates/index.html'
        ]
        
        for file_path in files_to_include:
            if os.path.exists(file_path):
                zipf.write(file_path)
                print(f"Added: {file_path}")
    
    print("SUCCESS: vercel-deploy.zip created")

def deploy_instructions():
    """Show deployment instructions"""
    print("\n" + "="*50)
    print("VERCEL DEPLOYMENT INSTRUCTIONS")
    print("="*50)
    print("1. Go to: https://vercel.com/")
    print("2. Sign up with GitHub account")
    print("3. Click 'Add New Project'")
    print("4. Upload vercel-deploy.zip")
    print("5. Configure:")
    print("   - Framework: Other")
    print("   - Build Command: (leave empty)")
    print("   - Output Directory: (leave empty)")
    print("6. Click 'Deploy'")
    print("7. Your app will be live at: https://your-project.vercel.app")
    print("\nAlternatively:")
    print("- Install Vercel CLI: npm i -g vercel")
    print("- Run: vercel --prod")

def main():
    print("Smart Ball Detection System - Vercel Deploy")
    print("Made by: Alvin, Daffa, Abidzar, Ridho")
    print("="*50)
    
    try:
        create_deployment_package()
        deploy_instructions()
        print("\nSUCCESS: Ready for Vercel deployment!")
        return True
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    main()
