# 🏈 IoT Ball Detection System

Sistem deteksi bola cerdas menggunakan AI dengan interface web yang modern dan responsif.

## 🚀 Fitur

- **Deteksi Bola Real-time**: Menggunakan model AI Roboflow untuk deteksi bola yang akurat
- **Upload Gambar**: Drag & drop atau pilih file gambar untuk dianalisis
- **Deteksi dari URL**: Masukkan URL gambar untuk deteksi langsung
- **Interface Modern**: Desain responsif dengan animasi dan feedback visual
- **API RESTful**: Endpoint API yang mudah digunakan
- **Status Monitoring**: Monitoring status sistem real-time

## 🛠️ Teknologi

- **Backend**: Node.js + Express.js
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **AI Model**: Roboflow Ball Detection v15
- **Styling**: CSS Grid, Flexbox, Gradients
- **Icons**: Font Awesome

## 📦 Instalasi

### Prerequisites
- Node.js (v14 atau lebih baru)
- npm atau yarn

### Langkah Instalasi

1. **Clone atau download proyek**
```bash
cd ball-detection-iot
```

2. **Install dependencies**
```bash
npm install
```

3. **Jalankan server**
```bash
npm start
```

4. **Buka browser**
```
http://localhost:3000
```

## 🔧 Penggunaan

### Melalui Web Interface

1. **Upload Gambar**:
   - Drag & drop gambar ke area upload
   - Atau klik area upload untuk memilih file
   - Klik tombol "Deteksi Bola"

2. **Deteksi dari URL**:
   - Masukkan URL gambar di input field
   - Klik tombol "Deteksi dari URL"

### Melalui API

#### Deteksi dari File Upload
```bash
curl -X POST http://localhost:3000/api/detect-ball \
  -F "image=@path/to/your/image.jpg"
```

#### Deteksi dari URL
```bash
curl -X POST http://localhost:3000/api/detect-ball-url \
  -H "Content-Type: application/json" \
  -d '{"imageUrl": "https://example.com/image.jpg"}'
```

#### Cek Status Sistem
```bash
curl http://localhost:3000/api/status
```

## 📁 Struktur Proyek

```
ball-detection-iot/
├── public/
│   └── index.html          # Interface web utama
├── uploads/                # Folder temporary untuk upload
├── server.js              # Server Node.js
├── package.json           # Dependencies dan scripts
└── README.md             # Dokumentasi
```

## 🔌 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Halaman utama web interface |
| POST | `/api/detect-ball` | Deteksi bola dari file upload |
| POST | `/api/detect-ball-url` | Deteksi bola dari URL |
| GET | `/api/status` | Status sistem |

### Response Format

```json
{
  "success": true,
  "predictions": [
    {
      "x": 150.5,
      "y": 200.3,
      "width": 50.2,
      "height": 50.2,
      "confidence": 0.95,
      "class": "ball"
    }
  ],
  "time": 245,
  "image": {
    "width": 640,
    "height": 480
  }
}
```

## ⚙️ Konfigurasi

### Environment Variables
- `PORT`: Port server (default: 3000)
- `ROBOFLOW_API_KEY`: API key Roboflow (sudah diset)

### File Upload Settings
- **Max file size**: 10MB
- **Allowed formats**: JPG, PNG, GIF, WebP
- **Storage**: Temporary (file dihapus setelah processing)

## 🎯 Model AI

- **Provider**: Roboflow
- **Model**: Ball Detection v15
- **Accuracy**: Tinggi untuk deteksi berbagai jenis bola
- **Processing Time**: ~200-500ms per gambar

## 🔒 Keamanan

- File upload dibatasi hanya untuk gambar
- File temporary dihapus setelah processing
- CORS diaktifkan untuk development
- Error handling yang komprehensif

## 🚀 Deployment

### Local Development
```bash
npm run dev  # Dengan nodemon untuk auto-restart
```

### Production
```bash
npm start
```

### Docker (Opsional)
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 📊 Monitoring

- Status sistem real-time
- Response time tracking
- Error logging
- File upload monitoring

## 🐛 Troubleshooting

### Masalah Umum

1. **Port sudah digunakan**
   ```bash
   # Ganti port di server.js atau set environment variable
   PORT=3001 npm start
   ```

2. **Upload gagal**
   - Pastikan file adalah gambar valid
   - Cek ukuran file (max 10MB)
   - Pastikan folder uploads ada

3. **API error**
   - Cek koneksi internet
   - Verifikasi API key Roboflow
   - Cek format request

## 🤝 Kontribusi

1. Fork proyek
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## 📄 License

MIT License - lihat file LICENSE untuk detail

## 📞 Support

Untuk pertanyaan atau dukungan:
- Buat issue di repository
- Email: <EMAIL>

---

**Dibuat dengan ❤️ untuk IoT dan AI enthusiasts** 