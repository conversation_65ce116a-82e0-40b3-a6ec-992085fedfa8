# Smart Ball Detection IoT Web

Website ini adalah sistem deteksi bola berbasis IoT yang dapat mengenali 6 jenis bola <PERSON> (Baseball, Basket, Sepak Bola, Golf, Tenis, Voli) dari gambar atau kamera secara otomatis menggunakan Computer Vision.

## Fitur Utama
- Deteksi bola dari gambar upload atau webcam
- Menampilkan jenis bola, confidence, dan posisi pada gambar
- Tam<PERSON><PERSON> modern, responsif, dan mudah digunakan
- Mendukung multi-class (bisa deteksi beberapa bola sekaligus)

## Jenis Bola yang Dideteksi
⚾ Baseball • 🏀 Basket • ⚽ Sepak Bola • 🏌️ Golf • 🎾 Tenis • 🏐 Voli

##

## Teknologi
- Node.js (Express) untuk backend
- HTML, CSS, JS (Vanilla) untuk frontend
- Roboflow API untuk deteksi bola (AI/Computer Vision)

## Cara Pakai
1. Upload gambar bola atau gunakan webcam
2. <PERSON><PERSON> "Deteksi Bola"
3. <PERSON><PERSON> deteksi akan muncul di layar

## Anggot<PERSON> Kelompo<PERSON>
Alvin, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

---

Powered by Roboflow AI & Flask (IoT Project) 