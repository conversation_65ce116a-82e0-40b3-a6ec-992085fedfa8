---
title: Smart Ball Detection System
emoji: ⚽
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.0
app_file: app.py
pinned: false
license: mit
---

# ⚽🏀🏐 Smart Ball Detection System

**AI-Powered Computer Vision for Sports Ball Recognition**

## 🎓 IoT Project by: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, R<PERSON><PERSON>

### 🚀 Features
- **🎯 Multi-Ball Detection**: Football, Basketball, Tennis, Volleyball, Golf, Baseball
- **🤖 AI-Powered**: Roboflow Computer Vision with high accuracy
- **📱 Easy to Use**: Upload any image and get instant results
- **🌐 Mobile Friendly**: Responsive design for all devices

### 🔧 Technology Stack
- **Frontend**: Gradio with custom CSS
- **AI Engine**: Roboflow Computer Vision API
- **Backend**: Python with PIL for image processing
- **Deployment**: Hugging Face Spaces

### 📖 How to Use
1. Upload an image containing sports balls
2. Click "Detect Balls" button
3. View the results with bounding boxes and confidence scores

### 🎯 Supported Ball Types
- ⚽ Football/Soccer
- 🏀 Basketball  
- 🎾 Tennis Ball
- 🏐 Volleyball
- 🏌️ Golf Ball
- ⚾ Baseball

---

**Built with ❤️ using modern web technologies**
