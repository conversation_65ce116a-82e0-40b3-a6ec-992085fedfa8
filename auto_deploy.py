#!/usr/bin/env python3
"""
Auto Deploy Smart Ball Detection System ke PythonAnywhere
"""
import requests
import json
import base64
import os
import time

class PythonAnywhereDeployer:
    def __init__(self):
        self.base_url = "https://www.pythonanywhere.com/api/v0/user"
        self.username = None
        self.token = None
        
    def setup_account(self):
        """Setup PythonAnywhere account"""
        print("🔑 Setting up PythonAnywhere account...")
        print("📝 Go to: https://www.pythonanywhere.com/registration/register/beginner/")
        print("📝 Create free account")
        print("📝 Go to Account → API Token → Create new token")
        
        self.username = input("Enter your PythonAnywhere username: ").strip()
        self.token = input("Enter your API token: ").strip()
        
        if not self.username or not self.token:
            print("❌ Username and token required!")
            return False
            
        return True
    
    def upload_files(self):
        """Upload files to PythonAnywhere"""
        print("📁 Uploading files...")
        
        files_to_upload = [
            'app_flask.py',
            'requirements.txt', 
            'wsgi.py',
            'templates/index.html'
        ]
        
        headers = {'Authorization': f'Token {self.token}'}
        
        for file_path in files_to_upload:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'rb') as f:
                        content = base64.b64encode(f.read()).decode()
                    
                    data = {
                        'content': content,
                        'path': f'/home/<USER>/smart-ball-detection/{file_path}'
                    }
                    
                    url = f"{self.base_url}/{self.username}/files/path/home/<USER>/smart-ball-detection/{file_path.replace('/', '%2F')}"
                    response = requests.post(url, headers=headers, json=data)
                    
                    if response.status_code == 201:
                        print(f"✅ Uploaded: {file_path}")
                    else:
                        print(f"❌ Failed to upload {file_path}: {response.text}")
                        
                except Exception as e:
                    print(f"❌ Error uploading {file_path}: {e}")
            else:
                print(f"⚠️ File not found: {file_path}")
    
    def create_webapp(self):
        """Create web app"""
        print("🌐 Creating web app...")
        
        headers = {'Authorization': f'Token {self.token}'}
        data = {
            'domain_name': f'{self.username}.pythonanywhere.com',
            'python_version': 'python310'
        }
        
        url = f"{self.base_url}/{self.username}/webapps/"
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 201:
            print("✅ Web app created!")
            return True
        else:
            print(f"❌ Failed to create web app: {response.text}")
            return False
    
    def setup_virtualenv(self):
        """Setup virtual environment"""
        print("🐍 Setting up virtual environment...")
        
        headers = {'Authorization': f'Token {self.token}'}
        
        # Create virtualenv
        data = {
            'command': f'mkvirtualenv --python=/usr/bin/python3.10 smart-ball-env && workon smart-ball-env && cd smart-ball-detection && pip install -r requirements.txt'
        }
        
        url = f"{self.base_url}/{self.username}/consoles/"
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 201:
            print("✅ Virtual environment setup started!")
            return True
        else:
            print(f"❌ Failed to setup virtualenv: {response.text}")
            return False
    
    def deploy(self):
        """Main deploy function"""
        print("🚀 Starting deployment to PythonAnywhere...")
        
        if not self.setup_account():
            return False
            
        self.upload_files()
        
        if self.create_webapp():
            self.setup_virtualenv()
            
            print("\n🎉 Deployment initiated!")
            print(f"🌐 Your app will be available at: https://{self.username}.pythonanywhere.com")
            print("\n📋 Manual steps to complete:")
            print("1. Go to PythonAnywhere Web tab")
            print("2. Set Virtualenv to: smart-ball-env")
            print("3. Edit WSGI file and paste content from wsgi.py")
            print("4. Click Reload")
            
            return True
        
        return False

def main():
    print("🎯 Smart Ball Detection System - Auto Deploy")
    print("Made by: Alvin, Daffa, Abidzar, Ridho")
    print("=" * 50)
    
    deployer = PythonAnywhereDeployer()
    
    if deployer.deploy():
        print("\n✅ Deployment successful!")
    else:
        print("\n❌ Deployment failed!")
        print("\n🔄 Alternative: Manual deployment")
        print("1. Go to: https://www.pythonanywhere.com/")
        print("2. Upload smart-ball-detection-deploy.zip")
        print("3. Follow DEPLOYMENT_GUIDE.md")

if __name__ == "__main__":
    main()
