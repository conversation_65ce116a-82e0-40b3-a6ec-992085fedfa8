#!/usr/bin/env python3
import requests
import json
import base64
import os
import time

def create_github_repo():
    print('Creating GitHub repository...')
    
    url = 'https://api.github.com/user/repos'
    
    repo_data = {
        'name': 'smart-ball-detection-iot',
        'description': 'Smart Ball Detection System - IoT Project by Alvin, Daffa, Abidzar, Ridho',
        'private': False,
        'auto_init': True
    }
    
    token = '****************************************'
    
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    response = requests.post(url, headers=headers, json=repo_data)
    
    if response.status_code == 201:
        repo_info = response.json()
        print(f'SUCCESS: Repository created: {repo_info["html_url"]}')
        return repo_info['full_name'], token
    else:
        print(f'ERROR: Failed to create repository: {response.text}')
        return None, None

def upload_files_to_github(repo_name, token):
    print('Uploading files to GitHub...')
    
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    files_to_upload = {
        'app_flask.py': 'Main Flask application',
        'requirements.txt': 'Python dependencies',
        'wsgi.py': 'WSGI configuration',
        'Procfile': 'Process configuration',
        'render.yaml': 'Render configuration',
        'README.md': 'Project documentation'
    }
    
    for file_path, description in files_to_upload.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                content_b64 = base64.b64encode(content.encode()).decode()
                
                data = {
                    'message': f'Add {description}',
                    'content': content_b64
                }
                
                url = f'https://api.github.com/repos/{repo_name}/contents/{file_path}'
                response = requests.put(url, headers=headers, json=data)
                
                if response.status_code == 201:
                    print(f'SUCCESS: Uploaded {file_path}')
                else:
                    print(f'ERROR: Failed to upload {file_path}: {response.text}')
                    
                time.sleep(1)
                
            except Exception as e:
                print(f'ERROR: uploading {file_path}: {e}')

def upload_template(repo_name, token):
    print('Uploading template...')
    
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    if os.path.exists('templates/index.html'):
        try:
            with open('templates/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            content_b64 = base64.b64encode(content.encode()).decode()
            
            data = {
                'message': 'Add HTML template',
                'content': content_b64
            }
            
            url = f'https://api.github.com/repos/{repo_name}/contents/templates/index.html'
            response = requests.put(url, headers=headers, json=data)
            
            if response.status_code == 201:
                print('SUCCESS: Uploaded templates/index.html')
            else:
                print(f'ERROR: Failed to upload template: {response.text}')
        except Exception as e:
            print(f'ERROR: uploading template: {e}')

def main():
    print('Smart Ball Detection System - GitHub Deploy')
    print('Made by: Alvin, Daffa, Abidzar, Ridho')
    print('=' * 50)
    
    repo_name, token = create_github_repo()
    if repo_name:
        upload_files_to_github(repo_name, token)
        upload_template(repo_name, token)
        print(f'SUCCESS: GitHub repo ready: https://github.com/{repo_name}')
        print('NEXT: Deploy to Render.com')
        print('1. Go to: https://dashboard.render.com')
        print('2. New > Web Service')
        print('3. Connect GitHub repo: smart-ball-detection-iot')
        print('4. Settings:')
        print('   - Name: smart-ball-detection-iot')
        print('   - Build Command: pip install -r requirements.txt')
        print('   - Start Command: gunicorn app_flask:app')
        print('   - Plan: Free')
        print('5. Deploy!')
        return True
    else:
        print('FAILED: Could not create GitHub repository')
        return False

if __name__ == "__main__":
    main()
