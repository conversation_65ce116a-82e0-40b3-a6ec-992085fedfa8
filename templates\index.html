<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚽ Smart Ball Detection System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 50px 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .input-section, .result-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .section-header .icon {
            font-size: 3rem;
            margin-bottom: 16px;
            display: block;
        }

        .section-header h2 {
            color: #1e293b;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        /* Tab System */
        .tab-nav {
            display: flex;
            margin-bottom: 30px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 16px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .tab-btn:not(.active) {
            background: #f1f5f9;
            color: #64748b;
        }

        .tab-btn:not(.active):hover {
            background: #e2e8f0;
            color: #475569;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Upload Area */
        .upload-area {
            border: 3px dashed #3b82f6;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #1d4ed8;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }

        .upload-text {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #64748b;
            font-size: 1rem;
        }

        .file-input {
            display: none;
        }

        /* Webcam Area */
        .webcam-info {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            margin-bottom: 20px;
        }

        .webcam-video {
            width: 100%;
            max-width: 500px;
            border-radius: 16px;
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        /* Buttons */
        .btn {
            border: none;
            border-radius: 16px;
            padding: 16px 32px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
        }

        .btn-full {
            width: 100%;
        }

        /* Preview Image */
        .preview-image {
            max-width: 100%;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        /* Result Cards */
        .result-card {
            border-radius: 20px;
            padding: 32px;
            margin: 16px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .success-card {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border: 2px solid #16a34a;
        }

        .error-card {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 2px solid #dc2626;
        }

        .info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px dashed #94a3b8;
            text-align: center;
            color: #64748b;
        }

        /* Loading */
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Info Box */
        .info-box {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        /* Footer */
        .footer {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .upload-area {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div style="font-size: 4rem; margin-bottom: 16px;">⚽🏀🏐</div>
            <h1>Smart Ball Detection System</h1>
            <p style="color: #64748b; font-size: 1.3rem; margin-top: 16px;">
                AI-Powered Computer Vision for Sports Ball Recognition
            </p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Input Section -->
            <div class="input-section">
                <div class="section-header">
                    <span class="icon">🎯</span>
                    <h2>Pilih Cara Input</h2>
                    <p style="color: #64748b;">Mau pake foto yang udah ada atau langsung dari kamera?</p>
                </div>

                <!-- Tab Navigation -->
                <div class="tab-nav">
                    <button id="uploadTab" class="tab-btn active">
                        📁 Upload Foto
                    </button>
                    <button id="webcamTab" class="tab-btn">
                        📹 Pake Kamera
                    </button>
                </div>

                <!-- Upload Tab Content -->
                <div id="uploadContent" class="tab-content active">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-text">Pilih File Foto</div>
                        <div class="upload-subtext">Bisa PNG, JPG, JPEG, WebP</div>
                        <input type="file" id="fileInput" class="file-input" accept="image/*">
                    </div>

                    <img id="previewImage" class="preview-image" style="display: none;">

                    <button id="detectBtn" class="btn btn-primary btn-full" disabled>
                        🎯 Deteksi Bola Sekarang
                    </button>
                </div>

                <!-- Webcam Tab Content -->
                <div id="webcamContent" class="tab-content">
                    <div class="webcam-info">
                        <div style="font-size: 2rem; margin-bottom: 16px;">📹</div>
                        <div style="font-size: 1.2rem; font-weight: 600; color: #1e293b; margin-bottom: 8px;">
                            Deteksi Langsung dari Kamera
                        </div>
                        <div style="color: #64748b;">
                            Taro bolanya di depan kamera ya bro
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button id="startWebcamBtn" class="btn btn-success">
                            📹 Nyalain Kamera
                        </button>

                        <video id="webcamVideo" class="webcam-video" style="display: none;"></video>
                        <canvas id="webcamCanvas" style="display: none;"></canvas>

                        <div id="webcamControls" style="display: none;">
                            <button id="captureBtn" class="btn btn-warning">
                                📸 Foto & Deteksi
                            </button>
                            <button id="stopWebcamBtn" class="btn btn-danger">
                                🛑 Matiin Kamera
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Info Box -->
                <div class="info-box">
                    <div style="color: #0c4a6e; font-weight: 600; margin-bottom: 8px;">
                        🎯 Jenis Bola yang Bisa Dideteksi (6 Macam)
                    </div>
                    <div style="color: #0c4a6e; font-size: 0.9rem;">
                        ⚾ Baseball • 🏀 Basket • ⚽ Sepak Bola • 🏌️ Golf • 🎾 Tenis • 🏐 Voli
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="result-section">
                <div class="section-header">
                    <span class="icon">🏆</span>
                    <h2>Hasil Deteksi</h2>
                    <p style="color: #64748b;">Hasil analisis IoT bakal muncul di sini</p>
                </div>

                <div id="loadingDiv" class="loading">
                    <div class="spinner"></div>
                    <div>🤖 Sistem IoT lagi analisis foto lu...</div>
                </div>

                <!-- Result Image Preview -->
                <img id="resultImage" class="preview-image" style="display: none;">

                <div id="resultDiv">
                    <div class="info-card">
                        <div style="font-size: 2rem; margin-bottom: 16px;">🤖</div>
                        <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 8px;">
                            Siap Buat Deteksi
                        </div>
                        <div>
                            Pilih cara input terus mulai deteksi bolanya!
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div style="color: #64748b; font-size: 1rem; margin-bottom: 12px;">
                <strong>🎓 IoT Project:</strong> Smart Ball Detection System using Computer Vision
            </div>
            <div style="color: #94a3b8; font-size: 0.9rem;">
                Made by: <strong>Alvin, Daffa, Abidzar, Ridho</strong> • Powered by Roboflow AI & Flask
            </div>
        </div>
    </div>

    <script>
        // Elements
        const uploadTab = document.getElementById('uploadTab');
        const webcamTab = document.getElementById('webcamTab');
        const uploadContent = document.getElementById('uploadContent');
        const webcamContent = document.getElementById('webcamContent');
        
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewImage = document.getElementById('previewImage');
        const detectBtn = document.getElementById('detectBtn');
        
        const startWebcamBtn = document.getElementById('startWebcamBtn');
        const webcamVideo = document.getElementById('webcamVideo');
        const webcamCanvas = document.getElementById('webcamCanvas');
        const webcamControls = document.getElementById('webcamControls');
        const captureBtn = document.getElementById('captureBtn');
        const stopWebcamBtn = document.getElementById('stopWebcamBtn');
        
        const loadingDiv = document.getElementById('loadingDiv');
        const resultDiv = document.getElementById('resultDiv');
        const resultImage = document.getElementById('resultImage');

        let selectedFile = null;
        let webcamStream = null;

        // Tab switching
        uploadTab.addEventListener('click', () => switchTab('upload'));
        webcamTab.addEventListener('click', () => switchTab('webcam'));

        function switchTab(tab) {
            if (tab === 'upload') {
                uploadTab.classList.add('active');
                webcamTab.classList.remove('active');
                uploadContent.classList.add('active');
                webcamContent.classList.remove('active');
                
                // Stop webcam if running
                if (webcamStream) {
                    stopWebcam();
                }
            } else {
                webcamTab.classList.add('active');
                uploadTab.classList.remove('active');
                webcamContent.classList.add('active');
                uploadContent.classList.remove('active');
            }
        }

        // Upload functionality
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelect);

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        detectBtn.addEventListener('click', detectBalls);

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('Pilih file gambar dong bro!');
                return;
            }

            selectedFile = file;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                previewImage.style.display = 'block';
                detectBtn.disabled = false;
            };
            reader.readAsDataURL(file);
        }

        // Webcam functionality
        startWebcamBtn.addEventListener('click', startWebcam);
        captureBtn.addEventListener('click', captureAndDetect);
        stopWebcamBtn.addEventListener('click', stopWebcam);

        async function startWebcam() {
            try {
                webcamStream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    } 
                });
                webcamVideo.srcObject = webcamStream;
                webcamVideo.play();
                webcamVideo.style.display = 'block';
                webcamControls.style.display = 'block';
                startWebcamBtn.style.display = 'none';
            } catch (error) {
                alert('Waduh, kameranya gabisa diakses: ' + error.message);
            }
        }

        function stopWebcam() {
            if (webcamStream) {
                webcamStream.getTracks().forEach(track => track.stop());
                webcamStream = null;
                webcamVideo.style.display = 'none';
                webcamControls.style.display = 'none';
                startWebcamBtn.style.display = 'block';
            }
        }

        function captureAndDetect() {
            if (!webcamStream) return;

            webcamCanvas.width = webcamVideo.videoWidth;
            webcamCanvas.height = webcamVideo.videoHeight;

            const ctx = webcamCanvas.getContext('2d');
            ctx.drawImage(webcamVideo, 0, 0);

            webcamCanvas.toBlob(blob => {
                const formData = new FormData();
                formData.append('file', blob, 'webcam_capture.jpg');
                sendDetectionRequest(formData);
            }, 'image/jpeg', 0.8);
        }

        function detectBalls() {
            if (!selectedFile) return;

            const formData = new FormData();
            formData.append('file', selectedFile);
            sendDetectionRequest(formData);
        }

        function sendDetectionRequest(formData) {
            loadingDiv.style.display = 'block';
            resultDiv.style.display = 'none';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('📦 Response received:', data);
                console.log('🖼️ Image data length:', data.image ? data.image.length : 'No image');
                console.log('📊 Predictions count:', data.predictions_count || 'Unknown');

                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';

                if (data.success) {
                    if (data.image) {
                        resultImage.src = data.image;
                        resultImage.style.display = 'block';
                        console.log('✅ Result image set successfully');
                    } else {
                        console.log('❌ No image data in response');
                        resultImage.style.display = 'none';
                    }
                    showResults(data.result);
                } else {
                    resultImage.style.display = 'none';
                    showError(data.error);
                }
            })
            .catch(error => {
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                resultImage.style.display = 'none';
                showError('Error jaringan: ' + error.message);
            });
        }

        function showResults(result) {
            if (result.error) {
                showError(result.error);
                return;
            }

            const predictions = result.predictions || [];
            const currentTime = new Date().toLocaleTimeString();

            if (predictions.length === 0) {
                resultDiv.innerHTML = `
                    <div class="info-card">
                        <div style="font-size: 2rem; margin-bottom: 16px;">🔍</div>
                        <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 8px; color: #1e40af;">
                            Gada Bola yang Kedeteksi
                        </div>
                        <div style="color: #1e40af;">
                            Coba upload foto yang ada bolanya bro
                        </div>
                        <div style="margin-top: 16px; color: #1e40af; opacity: 0.8; font-size: 0.9rem;">
                            ⏰ Waktu: ${currentTime}
                        </div>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="success-card">
                    <div style="text-align: center; margin-bottom: 24px;">
                        <div style="font-size: 4rem; margin-bottom: 16px;">🏆</div>
                        <div style="color: #15803d; font-size: 2rem; font-weight: 700; margin-bottom: 8px;">
                            ${predictions.length} Bola Berhasil Dideteksi!
                        </div>
                        <div style="color: #15803d; opacity: 0.8;">Sistem IoT Berhasil Deteksi</div>
                    </div>
                    
                    <div style="background: rgba(255,255,255,0.7); border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                        <div style="color: #15803d; font-weight: 600; margin-bottom: 16px; font-size: 1.1rem;">
                            📊 Detail Deteksi:
                        </div>
            `;

            predictions.forEach((pred, i) => {
                const confidence = (pred.confidence * 100).toFixed(1);
                html += `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; margin: 8px 0; background: rgba(22,163,74,0.1); border-radius: 8px; border-left: 4px solid #16a34a;">
                        <div>
                            <span style="font-weight: 600; color: #15803d;">
                                ${i + 1}. ${pred.class.toUpperCase()}
                            </span>
                        </div>
                        <div style="text-align: right;">
                            <div style="color: #15803d; font-weight: 600;">${confidence}%</div>
                            <div style="color: #15803d; opacity: 0.7; font-size: 0.8rem;">
                                (${Math.round(pred.x)}, ${Math.round(pred.y)})
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: rgba(22,163,74,0.1); border-radius: 12px; color: #15803d;">
                        <div><strong>⏰ Waktu:</strong> ${currentTime}</div>
                        <div><strong>🎯 Akurasi:</strong> Tinggi</div>
                    </div>
                </div>
            `;

            resultDiv.innerHTML = html;
        }

        function showError(error) {
            resultDiv.innerHTML = `
                <div class="error-card">
                    <div style="text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">❌</div>
                        <div style="color: #dc2626; font-size: 1.5rem; font-weight: 600; margin-bottom: 16px;">
                            Deteksi Gagal
                        </div>
                        <div style="color: #dc2626;">${error}</div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
