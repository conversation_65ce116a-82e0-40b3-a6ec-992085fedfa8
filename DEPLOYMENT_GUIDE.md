# 🚀 Deployment Guide - Smart Ball Detection System

**IoT Project by: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>**

## 🐍 Option 1: PythonAnywhere (Recommended - Free & Easy)

### Step 1: Sign Up
1. Go to: https://www.pythonanywhere.com/
2. Create free account
3. Verify email

### Step 2: Upload Files
1. Go to **Files** tab
2. Create folder: `smart-ball-detection`
3. Upload all files:
   - `app_flask.py`
   - `requirements.txt`
   - `wsgi.py`
   - `templates/index.html`
   - `samples/` folder

### Step 3: Install Dependencies
1. Open **Bash Console**
2. Create virtualenv:
   ```bash
   mkvirtualenv --python=/usr/bin/python3.10 smart-ball-env
   cd smart-ball-detection
   pip install -r requirements.txt
   ```

### Step 4: Configure Web App
1. Go to **Web** tab
2. Click **Add a new web app**
3. Choose **Manual configuration**
4. Select **Python 3.10**
5. Set **Virtualenv**: `smart-ball-env`
6. Edit **WSGI file** and replace content with `wsgi.py`

### Step 5: Go Live!
- Your app will be at: `https://yourusername.pythonanywhere.com`
- Click **Reload** to update changes

---

## 🌐 Option 2: Render.com (Professional)

### Step 1: GitHub Setup
1. Create GitHub repo: `smart-ball-detection-iot`
2. Push all files to repo

### Step 2: Deploy to Render
1. Go to: https://dashboard.render.com
2. **New → Web Service**
3. Connect GitHub repo
4. Settings:
   - **Name**: `smart-ball-detection-iot`
   - **Language**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn app_flask:app`
   - **Plan**: `Free`

### Step 3: Environment Variables
- `FLASK_ENV` = `production`
- `PYTHON_VERSION` = `3.10`

---

## ⚡ Option 3: Railway (Fast Deploy)

### Step 1: GitHub Setup
Same as Render option

### Step 2: Deploy to Railway
1. Go to: https://railway.app
2. **New Project → Deploy from GitHub**
3. Select your repo
4. Auto-deploy in 2-3 minutes!

---

## 📱 Testing Deployment

After deployment, test these features:
- ✅ Upload image detection
- ✅ Webcam functionality (requires HTTPS)
- ✅ Mobile responsiveness
- ✅ All ball types detection

## 🔧 Troubleshooting

### Common Issues:
1. **Webcam not working**: Ensure HTTPS is enabled
2. **Dependencies error**: Check Python version compatibility
3. **Template not found**: Verify file paths are correct
4. **API timeout**: Check Roboflow API key and limits

### Support:
- Check deployment logs in platform dashboard
- Verify all files uploaded correctly
- Test locally first: `python app_flask.py`

---

## 🎯 Final URLs

After successful deployment:
- **PythonAnywhere**: `https://yourusername.pythonanywhere.com`
- **Render**: `https://smart-ball-detection-iot.onrender.com`
- **Railway**: `https://smart-ball-detection-production.up.railway.app`

**Made by: Alvin, Daffa, Abidzar, Ridho**
