const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_IMAGE_PATH = './test-image.jpg'; // You can add a test image here

// Test functions
async function testStatusAPI() {
    try {
        console.log('🔍 Testing Status API...');
        const response = await axios.get(`${BASE_URL}/api/status`);
        console.log('✅ Status API Response:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Status API Error:', error.message);
        return false;
    }
}

async function testAnimalDetectionAPI() {
    try {
        console.log('🦁 Testing Animal Detection API...');

        // Check if test image exists
        if (!fs.existsSync(TEST_IMAGE_PATH)) {
            console.log('⚠️  Test image not found. Creating a simple test...');
            // Test with a simple form data
            const formData = new FormData();
            formData.append('image', Buffer.from('fake-image-data'), {
                filename: 'test.jpg',
                contentType: 'image/jpeg'
            });

            const response = await axios.post(`${BASE_URL}/api/detect-animal`, formData, {
                headers: formData.getHeaders()
            });

            console.log('✅ Animal Detection API Response:', response.data);
            return true;
        } else {
            // Test with actual image file
            const formData = new FormData();
            formData.append('image', fs.createReadStream(TEST_IMAGE_PATH));

            const response = await axios.post(`${BASE_URL}/api/detect-animal`, formData, {
                headers: formData.getHeaders()
            });

            console.log('✅ Animal Detection API Response:', response.data);
            return true;
        }
    } catch (error) {
        console.error('❌ Animal Detection API Error:', error.response?.data || error.message);
        return false;
    }
}

async function testInvalidRequest() {
    try {
        console.log('🚫 Testing Invalid Request...');

        // Test without image file
        const response = await axios.post(`${BASE_URL}/api/detect-animal`);
        console.log('❌ Should have failed but got response:', response.data);
        return false;
    } catch (error) {
        if (error.response?.status === 400) {
            console.log('✅ Invalid request properly rejected:', error.response.data);
            return true;
        } else {
            console.error('❌ Unexpected error:', error.message);
            return false;
        }
    }
}

async function test404Endpoint() {
    try {
        console.log('🔍 Testing 404 Endpoint...');
        const response = await axios.get(`${BASE_URL}/api/nonexistent`);
        console.log('❌ Should have failed but got response:', response.data);
        return false;
    } catch (error) {
        if (error.response?.status === 404) {
            console.log('✅ 404 endpoint properly handled:', error.response.data);
            return true;
        } else {
            console.error('❌ Unexpected error:', error.message);
            return false;
        }
    }
}

// Main test runner
async function runTests() {
    console.log('🧪 Starting API Tests...\n');

    const tests = [
        { name: 'Status API', fn: testStatusAPI },
        { name: 'Animal Detection API', fn: testAnimalDetectionAPI },
        { name: 'Invalid Request', fn: testInvalidRequest },
        { name: '404 Endpoint', fn: test404Endpoint }
    ];

    let passed = 0;
    let total = tests.length;

    for (const test of tests) {
        console.log(`\n📋 Running: ${test.name}`);
        console.log('─'.repeat(50));

        const result = await test.fn();
        if (result) {
            passed++;
            console.log(`✅ ${test.name}: PASSED`);
        } else {
            console.log(`❌ ${test.name}: FAILED`);
        }
    }

    console.log('\n📊 Test Results:');
    console.log('─'.repeat(50));
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${total - passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (passed === total) {
        console.log('\n🎉 All tests passed! API is working correctly.');
    } else {
        console.log('\n⚠️  Some tests failed. Please check the server and try again.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testStatusAPI,
    testAnimalDetectionAPI,
    testInvalidRequest,
    test404Endpoint,
    runTests
}; 