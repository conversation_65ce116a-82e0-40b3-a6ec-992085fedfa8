# 🚀 Deployment Guide - Animal Detection IoT

Panduan lengkap untuk deploy aplikasi Animal Detection ke berbagai platform cloud.

## 📋 Prerequisites

- Node.js 16+ terinstall
- Git repository sudah siap
- Akun di platform deployment yang dipilih

## 🎯 Platform Deployment

### 1. Render (Recommended)

**Keunggulan**: Free tier, mudah digunakan, auto-deploy

1. **Sign up di Render**
   - Kunjungi [render.com](https://render.com)
   - Buat akun baru

2. **Connect Repository**
   - Klik "New +" → "Web Service"
   - Connect ke GitHub/GitLab repository
   - Pilih repository `animal-detection-iot`

3. **Configure Service**
   - **Name**: `animal-detection-iot`
   - **Environment**: `Node`
   - **Build Command**: `npm install`
   - **Start Command**: `npm start`
   - **Plan**: Free

4. **Environment Variables** (Opsional)
   ```
   NODE_ENV=production
   PORT=10000
   ```

5. **Deploy**
   - <PERSON>lik "Create Web Service"
   - Tunggu build selesai (2-3 menit)
   - Aplikasi akan live di `https://your-app-name.onrender.com`

### 2. Railway

**Keunggulan**: Free tier, fast deployment, good performance

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login & Deploy**
   ```bash
   railway login
   railway init
   railway up
   ```

3. **Configure**
   - Set environment variables di Railway dashboard
   - Configure domain jika diperlukan

### 3. Heroku

**Keunggulan**: Mature platform, banyak add-ons

1. **Install Heroku CLI**
   ```bash
   # Windows
   winget install --id=Heroku.HerokuCLI
   
   # macOS
   brew tap heroku/brew && brew install heroku
   ```

2. **Login & Deploy**
   ```bash
   heroku login
   heroku create your-app-name
   git push heroku main
   ```

3. **Open App**
   ```bash
   heroku open
   ```

### 4. Vercel

**Keunggulan**: Fast, good for frontend-heavy apps

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy**
   ```bash
   vercel
   ```

3. **Follow prompts**
   - Link to existing project: No
   - Project name: animal-detection-iot
   - Directory: ./
   - Override settings: No

### 5. Docker Deployment

**Keunggulan**: Consistent environment, portable

1. **Build Image**
   ```bash
   docker build -t animal-detection-iot .
   ```

2. **Run Container**
   ```bash
   docker run -p 3000:3000 animal-detection-iot
   ```

3. **Docker Compose** (Optional)
   ```yaml
   # docker-compose.yml
   version: '3.8'
   services:
     app:
       build: .
       ports:
         - "3000:3000"
       environment:
         - NODE_ENV=production
   ```

## 🔧 Environment Variables

### Production Variables
```bash
NODE_ENV=production
PORT=3000
ROBOFLOW_API_KEY=Am7lrBZW1eYzrwt0rWvc
ROBOFLOW_API_URL=https://serverless.roboflow.com/animal-recognition-2xjbx/3
```

### Development Variables
```bash
NODE_ENV=development
PORT=3000
```

## 📊 Monitoring & Health Checks

### Health Check Endpoint
```
GET /api/status
```

**Response:**
```json
{
  "status": "online",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "Animal Detection API",
  "version": "1.0.0"
}
```

### Custom Health Check
```bash
curl https://your-app-url.com/api/status
```

## 🐛 Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   # Check Node.js version
   node --version
   
   # Clear npm cache
   npm cache clean --force
   
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Port Issues**
   - Pastikan menggunakan `process.env.PORT` di server.js
   - Platform cloud biasanya set PORT secara otomatis

3. **API Key Issues**
   - Pastikan API key Roboflow valid
   - Check rate limits
   - Verify API endpoint URL

4. **File Upload Issues**
   - Pastikan folder `uploads/` ada
   - Check file permissions
   - Verify multer configuration

### Debug Commands

```bash
# Check logs
heroku logs --tail
railway logs
vercel logs

# Check environment
echo $NODE_ENV
echo $PORT

# Test API locally
npm test
```

## 🔒 Security Considerations

1. **API Key Protection**
   - Jangan expose API key di frontend
   - Gunakan environment variables
   - Rotate keys regularly

2. **File Upload Security**
   - Validate file types
   - Set file size limits
   - Scan for malware (optional)

3. **CORS Configuration**
   - Set proper CORS origins
   - Limit allowed methods
   - Configure credentials policy

## 📈 Performance Optimization

1. **Image Optimization**
   - Compress images before upload
   - Use appropriate formats (WebP, JPEG)
   - Set reasonable size limits

2. **Caching**
   - Cache API responses
   - Use CDN for static files
   - Implement browser caching

3. **Database** (Future Enhancement)
   - Store detection history
   - Cache frequent results
   - Use Redis for session storage

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
# .github/workflows/deploy.yml
name: Deploy to Render

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Render
        uses: johnbeynon/render-deploy-action@v0.0.1
        with:
          service-id: ${{ secrets.RENDER_SERVICE_ID }}
          api-key: ${{ secrets.RENDER_API_KEY }}
```

## 📞 Support

Jika mengalami masalah deployment:

1. **Check Documentation**
   - Platform-specific docs
   - Node.js deployment guides
   - Express.js best practices

2. **Community Support**
   - Stack Overflow
   - GitHub Issues
   - Platform forums

3. **Debug Steps**
   - Check logs
   - Test locally
   - Verify configuration

---

**🎓 Happy Deploying! 🚀** 